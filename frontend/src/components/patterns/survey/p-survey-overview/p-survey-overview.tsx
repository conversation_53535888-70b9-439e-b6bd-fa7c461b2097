import { Component, h, Prop, State, Element } from '@stencil/core';
import { getResponsesApi, ResponseFilters } from '../p-survey-responses/helpers';

@Component({
  tag: 'p-survey-overview',
  styleUrl: 'p-survey-overview.css',
  shadow: true,
})
export class PSurveyOverview {
  @Element() el: HTMLElement;
  @Prop() surveyId: string;
  @Prop() survey: any;

  @State() selectedTimeFilter: string = 'all-time';
  @State() customStartDate: string = '';
  @State() customEndDate: string = '';
  @State() firstResponseDate: string = '';
  @State() validationError: string = '';
  @State() totalCount: number = 0;
  @State() loading: boolean = true;
  @State() error: string = null;
  @State() analytics: any = {};

  private timeFilterOptions = [
    { label: 'All time', value: 'all-time' },
    { label: 'Last 7 days', value: '7-days' },
    { label: 'Last 30 days', value: '30-days' },
    { label: 'Last 90 days', value: '90-days' },
    { label: 'Custom Range', value: 'custom-range' },
  ];

  async componentDidLoad() {
    await this.fetchOverviewData();
    await this.setFirstResponseDate();
  }

  private async fetchOverviewData() {
    if (!this.surveyId) return;

    this.loading = true;
    this.error = null;

    try {
      const filters: ResponseFilters = {
        timeFilter: this.selectedTimeFilter,
        page: 1,
        limit: 1, // We only need the analytics data, not the actual responses
      };

      // Add custom date range if selected
      if (this.selectedTimeFilter === 'custom-range') {
        filters.customStartDate = this.customStartDate;
        filters.customEndDate = this.customEndDate;
      }

      const result = await getResponsesApi(this.surveyId, filters);

      if (result.success) {
        this.totalCount = result.payload.totalCount || 0;
        this.analytics = result.payload || {};

        // Render graph if we have data
        if (this.analytics.responsesByDay) {
          setTimeout(async () => {
            await this.renderGraph();
          }, 100);
        }
      } else {
        this.error = result.message;
      }
    } catch (error) {
      console.error('Error fetching overview data:', error);
      this.error = 'Failed to load overview data';
    } finally {
      this.loading = false;
    }
  }

  private async setFirstResponseDate() {
    try {
      // Get the first response date for date picker constraints
      const result = await getResponsesApi(this.surveyId, {
        timeFilter: 'all-time',
        page: 1,
        limit: 1,
      });
      if (result.success && result.payload.responses && result.payload.responses.length > 0) {
        const firstResponse = result.payload.responses[result.payload.responses.length - 1];
        if (firstResponse && firstResponse.created_at) {
          this.firstResponseDate = new Date(firstResponse.created_at).toISOString().split('T')[0];
        }
      }
    } catch (error) {
      console.error('Error setting first response date:', error);
    }
  }

  private handleTimeFilterChange = async (event: any) => {
    const newValue = event.detail.value;

    if (newValue === 'custom-range') {
      this.selectedTimeFilter = newValue;
      this.customStartDate = '';
      this.customEndDate = '';
      this.validationError = '';
    } else {
      this.selectedTimeFilter = newValue;
      await this.fetchOverviewData();
    }
  };

  private applyInlineCustomDateRange = async () => {
    if (!this.customStartDate || !this.customEndDate) {
      this.validationError = 'Please select both start and end dates';
      return;
    }

    if (new Date(this.customStartDate) > new Date(this.customEndDate)) {
      this.validationError = 'Start date must be before end date';
      return;
    }

    this.validationError = '';
    await this.fetchOverviewData();
  };

  private getCompletionRate(): string {
    // For now, return a placeholder. This would need to be calculated based on actual survey data
    return this.analytics.completionRate ? `${Math.round(this.analytics.completionRate)}%` : '0%';
  }

  private getAverageTime(): string {
    // For now, return a placeholder. This would need to be calculated based on actual response times
    return this.analytics.averageTime ? `${Math.round(this.analytics.averageTime)}m` : '0m';
  }

  private async renderGraph() {
    try {
      const graphElement = this.el.shadowRoot?.querySelector('#responses-graph');
      if (!graphElement || !this.analytics.responsesByDay) {
        console.log('Graph element not found or no data available');
        return;
      }

      // Import Plotly dynamically
      const Plotly = await import('plotly.js-dist-min');

      // Prepare data for the graph
      const responsesByDay = this.analytics.responsesByDay || [];
      const dates = responsesByDay.map((item: any) => item.date);
      const counts = responsesByDay.map((item: any) => item.count);

      const data = [
        {
          x: dates,
          y: counts,
          type: 'scatter',
          mode: 'lines+markers',
          line: { color: 'var(--color__blue--600)', width: 3 },
          marker: { color: 'var(--color__blue--600)', size: 6 },
          name: 'Responses',
        },
      ];

      const layout = {
        title: {
          text: 'Responses Over Time',
          font: { size: 16, color: 'var(--color__grey--800)' },
        },
        xaxis: {
          title: 'Date',
          type: 'date',
          showgrid: true,
          gridcolor: 'var(--color__grey--200)',
        },
        yaxis: {
          title: 'Number of Responses',
          showgrid: true,
          gridcolor: 'var(--color__grey--200)',
        },
        plot_bgcolor: 'transparent',
        paper_bgcolor: 'transparent',
        margin: { l: 50, r: 50, t: 50, b: 50 },
        font: { family: 'inherit', color: 'var(--color__grey--700)' },
      };

      const config = {
        displayModeBar: false,
        responsive: true,
      };

      await Plotly.newPlot(graphElement, data, layout, config);

      // Add hover effects
      setTimeout(() => {
        (graphElement as any).on('plotly_hover', () => {
          (graphElement as HTMLElement).style.cursor = 'pointer';
        });

        (graphElement as any).on('plotly_unhover', () => {
          (graphElement as HTMLElement).style.cursor = 'default';
        });
      }, 200);

      // Add click event handler to graph points
      (graphElement as any).on('plotly_click', (eventData: any) => {
        if (eventData.points && eventData.points.length > 0) {
          const clickedDate = eventData.points[0].x;
          const responseCount = eventData.points[0].y;

          // Format the date for display
          const formattedDate = new Date(clickedDate).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
          });

          console.log(`Date: ${formattedDate}\nResponses: ${responseCount}`);
        }
      });
    } catch (error) {
      console.error('Error rendering graph:', error);
    }
  }

  private getResponsesPerDay(): string {
    if (!this.analytics.responsesByDay || this.analytics.responsesByDay.length === 0) {
      return '0';
    }

    const totalDays = this.analytics.responsesByDay.length;
    const totalResponses = this.analytics.responsesByDay.reduce(
      (sum: number, day: any) => sum + day.count,
      0,
    );
    const avgPerDay = totalResponses / totalDays;

    return avgPerDay < 1 ? avgPerDay.toFixed(1) : Math.round(avgPerDay).toString();
  }

  render() {
    return (
      <div style={{ width: '100%' }}>
        {/* Header with title and filters */}
        <l-row justifyContent="space-between" align="center">
          <e-text variant="heading">Overview</e-text>

          {/* Custom Range Date Inputs - shown inline when custom range is selected */}
          {this.selectedTimeFilter === 'custom-range' && (
            <div
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: '0.75em',
                flex: '1',
                justifyContent: 'center',
              }}
            >
              <input
                type="date"
                value={this.customStartDate}
                min={this.firstResponseDate}
                max={new Date().toISOString().split('T')[0]}
                onInput={e => {
                  this.customStartDate = (e.target as HTMLInputElement).value;
                  this.validationError = '';
                  // Auto-apply when both dates are set
                  if (this.customStartDate && this.customEndDate) {
                    this.applyInlineCustomDateRange();
                  }
                }}
                style={{
                  padding: '0.5em',
                  border: 'var(--border__input)',
                  borderRadius: 'var(--border-radius)',
                  fontSize: '0.875em',
                  width: '140px',
                }}
              />

              <e-text style={{ color: 'var(--color__grey--600)', fontSize: '0.875em' }}>to</e-text>

              <input
                type="date"
                value={this.customEndDate}
                min={this.customStartDate || this.firstResponseDate}
                max={new Date().toISOString().split('T')[0]}
                onInput={e => {
                  this.customEndDate = (e.target as HTMLInputElement).value;
                  this.validationError = '';
                  // Auto-apply when both dates are set
                  if (this.customStartDate && this.customEndDate) {
                    this.applyInlineCustomDateRange();
                  }
                }}
                style={{
                  padding: '0.5em',
                  border: 'var(--border__input)',
                  borderRadius: 'var(--border-radius)',
                  fontSize: '0.875em',
                  width: '140px',
                }}
              />
            </div>
          )}

          <div style={{ position: 'relative' }}>
            <e-select
              value={this.selectedTimeFilter}
              options={JSON.stringify(this.timeFilterOptions)}
              name="timeFilter"
              variant="narrow"
              onSelectChangeEvent={this.handleTimeFilterChange}
            />
          </div>
        </l-row>

        {/* Validation Error for inline custom range */}
        {this.selectedTimeFilter === 'custom-range' && this.validationError && (
          <div style={{ marginTop: '0.5em', textAlign: 'center' }}>
            <e-text
              style={{
                color: 'var(--color__red--600)',
                fontSize: '0.875em',
                display: 'inline-block',
                padding: '0.5em 1em',
                backgroundColor: 'var(--color__red--50)',
                border: '1px solid var(--color__red--200)',
                borderRadius: 'var(--border-radius)',
              }}
            >
              {this.validationError}
            </e-text>
          </div>
        )}

        <l-spacer value={2}></l-spacer>

        {/* Error State */}
        {this.error && (
          <c-card>
            <div style={{ textAlign: 'center', padding: '2em' }}>
              <e-text>
                <strong>{this.error}</strong>
              </e-text>
            </div>
          </c-card>
        )}

        {/* Overview Cards */}
        {!this.error && (
          <div>
            {/* Main Analytics Cards */}
            <c-card>
              <l-row>
                <div style={{ textAlign: 'center', flex: '1' }}>
                  <e-text variant="footnote">TOTAL RESPONSES</e-text>
                  <l-spacer value={0.25}></l-spacer>
                  <e-text variant="display">{this.loading ? '...' : this.totalCount}</e-text>
                </div>
                <div style={{ textAlign: 'center', flex: '1' }}>
                  <e-text variant="footnote">RESPONSES PER DAY</e-text>
                  <l-spacer value={0.25}></l-spacer>
                  <e-text variant="display">
                    {this.loading ? '...' : this.getResponsesPerDay()}
                  </e-text>
                </div>
                <div style={{ textAlign: 'center', flex: '1' }}>
                  <e-text variant="footnote">AVG. COMPLETION TIME</e-text>
                  <l-spacer value={0.25}></l-spacer>
                  <e-text variant="display">{this.loading ? '...' : this.getAverageTime()}</e-text>
                </div>
              </l-row>
            </c-card>

            <l-spacer value={2}></l-spacer>

            {/* Responses Over Time Graph */}
            <c-card>
              <div style={{ padding: '1em' }}>
                <e-text variant="subheading">Responses Over Time</e-text>
                <l-spacer value={1}></l-spacer>

                {this.loading ? (
                  <div
                    style={{
                      height: '300px',
                      backgroundColor: 'var(--color__grey--50)',
                      borderRadius: '4px',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}
                  >
                    <e-text variant="footnote">Loading graph...</e-text>
                  </div>
                ) : this.analytics.responsesByDay && this.analytics.responsesByDay.length > 0 ? (
                  <div
                    id="responses-graph"
                    style={{
                      height: '300px',
                      width: '100%',
                    }}
                  ></div>
                ) : (
                  <div
                    style={{
                      height: '300px',
                      backgroundColor: 'var(--color__grey--50)',
                      borderRadius: '4px',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      flexDirection: 'column',
                      gap: '0.5em',
                    }}
                  >
                    <e-text variant="footnote">No response data available</e-text>
                    <e-text variant="footnote" style={{ color: 'var(--color__grey--600)' }}>
                      Graph will appear once you have survey responses
                    </e-text>
                  </div>
                )}
              </div>
            </c-card>
          </div>
        )}
      </div>
    );
  }
}
