import { Component, h, Prop, State, Element } from '@stencil/core';
import { getResponsesApi, ResponseFilters } from '../p-survey-responses/helpers';

@Component({
  tag: 'p-survey-overview',
  styleUrl: 'p-survey-overview.css',
  shadow: true,
})
export class PSurveyOverview {
  @Element() el: HTMLElement;
  @Prop() surveyId: string;
  @Prop() survey: any;

  @State() selectedTimeFilter: string = 'all-time';
  @State() customStartDate: string = '';
  @State() customEndDate: string = '';
  @State() firstResponseDate: string = '';
  @State() validationError: string = '';
  @State() totalCount: number = 0;
  @State() loading: boolean = true;
  @State() error: string = null;
  @State() analytics: any = {};

  private timeFilterOptions = [
    { label: 'All time', value: 'all-time' },
    { label: 'Last 7 days', value: '7-days' },
    { label: 'Last 30 days', value: '30-days' },
    { label: 'Last 90 days', value: '90-days' },
    { label: 'Custom Range', value: 'custom-range' },
  ];

  async componentDidLoad() {
    await this.fetchOverviewData();
    await this.setFirstResponseDate();
  }

  private async fetchOverviewData() {
    if (!this.surveyId) return;

    this.loading = true;
    this.error = null;

    try {
      const filters: ResponseFilters = {
        timeFilter: this.selectedTimeFilter,
        page: 1,
        limit: 1, // We only need the analytics data, not the actual responses
      };

      // Add custom date range if selected
      if (this.selectedTimeFilter === 'custom-range') {
        filters.customStartDate = this.customStartDate;
        filters.customEndDate = this.customEndDate;
      }

      const result = await getResponsesApi(this.surveyId, filters);

      if (result.success) {
        this.totalCount = result.payload.totalCount || 0;
        this.analytics = result.payload || {};

        // Render graph if we have data
        if (this.analytics.responsesByDay) {
          setTimeout(async () => {
            await this.renderGraph();
          }, 100);
        }
      } else {
        this.error = result.message;
      }
    } catch (error) {
      console.error('Error fetching overview data:', error);
      this.error = 'Failed to load overview data';
    } finally {
      this.loading = false;
    }
  }

  private async setFirstResponseDate() {
    try {
      // Get the first response date for date picker constraints
      const result = await getResponsesApi(this.surveyId, {
        timeFilter: 'all-time',
        page: 1,
        limit: 1,
      });
      if (result.success && result.payload.responses && result.payload.responses.length > 0) {
        const firstResponse = result.payload.responses[result.payload.responses.length - 1];
        if (firstResponse && firstResponse.created_at) {
          this.firstResponseDate = new Date(firstResponse.created_at).toISOString().split('T')[0];
        }
      }
    } catch (error) {
      console.error('Error setting first response date:', error);
    }
  }

  private handleTimeFilterChange = async (event: any) => {
    const newValue = event.detail.value;

    if (newValue === 'custom-range') {
      this.selectedTimeFilter = newValue;
      this.customStartDate = '';
      this.customEndDate = '';
      this.validationError = '';
    } else {
      this.selectedTimeFilter = newValue;
      await this.fetchOverviewData();
    }
  };

  private applyInlineCustomDateRange = async () => {
    if (!this.customStartDate || !this.customEndDate) {
      this.validationError = 'Please select both start and end dates';
      return;
    }

    if (new Date(this.customStartDate) > new Date(this.customEndDate)) {
      this.validationError = 'Start date must be before end date';
      return;
    }

    this.validationError = '';
    await this.fetchOverviewData();
  };

  private getCompletionRate(): string {
    // For now, return a placeholder. This would need to be calculated based on actual survey data
    return this.analytics.completionRate ? `${Math.round(this.analytics.completionRate)}%` : '0%';
  }

  private getAverageTime(): string {
    // For now, return a placeholder. This would need to be calculated based on actual response times
    return this.analytics.averageTime ? `${Math.round(this.analytics.averageTime)}m` : '0m';
  }

  private async renderGraph() {
    try {
      const graphElement = this.el.shadowRoot?.querySelector('#responses-graph');
      if (!graphElement || !this.analytics.responsesByDay) {
        console.log('Graph element not found or no data available');
        return;
      }

      // Import Plotly dynamically
      const Plotly = await import('plotly.js-dist-min');

      // Prepare data for the graph
      const responsesByDay = this.analytics.responsesByDay || [];
      const dates = responsesByDay.map((item: any) => item.date);
      const counts = responsesByDay.map((item: any) => item.count);

      const data = [
        {
          x: dates,
          y: counts,
          type: 'scatter',
          mode: 'lines+markers',
          line: { color: 'var(--color__blue--600)', width: 3 },
          marker: { color: 'var(--color__blue--600)', size: 6 },
          name: 'Responses',
        },
      ];

      const layout = {
        title: {
          text: 'Responses Over Time',
          font: { size: 16, color: 'var(--color__grey--800)' },
        },
        xaxis: {
          title: 'Date',
          type: 'date',
          showgrid: true,
          gridcolor: 'var(--color__grey--200)',
        },
        yaxis: {
          title: 'Number of Responses',
          showgrid: true,
          gridcolor: 'var(--color__grey--200)',
        },
        plot_bgcolor: 'transparent',
        paper_bgcolor: 'transparent',
        margin: { l: 50, r: 50, t: 50, b: 50 },
        font: { family: 'inherit', color: 'var(--color__grey--700)' },
      };

      const config = {
        displayModeBar: false,
        responsive: true,
      };

      await Plotly.newPlot(graphElement, data, layout, config);

      // Add hover effects
      setTimeout(() => {
        (graphElement as any).on('plotly_hover', () => {
          (graphElement as HTMLElement).style.cursor = 'pointer';
        });

        (graphElement as any).on('plotly_unhover', () => {
          (graphElement as HTMLElement).style.cursor = 'default';
        });
      }, 200);

      // Add click event handler to graph points
      (graphElement as any).on('plotly_click', (eventData: any) => {
        if (eventData.points && eventData.points.length > 0) {
          const clickedDate = eventData.points[0].x;
          const responseCount = eventData.points[0].y;

          // Format the date for display
          const formattedDate = new Date(clickedDate).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
          });

          console.log(`Date: ${formattedDate}\nResponses: ${responseCount}`);
        }
      });
    } catch (error) {
      console.error('Error rendering graph:', error);
    }
  }

  private getResponsesPerDay(): string {
    if (!this.analytics.responsesByDay || this.analytics.responsesByDay.length === 0) {
      return '0';
    }

    const totalDays = this.analytics.responsesByDay.length;
    const totalResponses = this.analytics.responsesByDay.reduce(
      (sum: number, day: any) => sum + day.count,
      0,
    );
    const avgPerDay = totalResponses / totalDays;

    return avgPerDay < 1 ? avgPerDay.toFixed(1) : Math.round(avgPerDay).toString();
  }

  private getTrendTooltipText(): string {
    const trendComponents = this.analytics.responseRateTrendComponents;
    const headerText = this.analytics.headerText || '';

    if (!trendComponents) {
      return 'Percentage change for the selected time range vs the previous time range of the same value';
    }

    let baseExplanation = '';

    // Determine base explanation based on header text
    if (headerText.includes('WEEK')) {
      baseExplanation = 'Percentage change for the selected time range vs the previous week';
    } else if (headerText.includes('MONTH')) {
      baseExplanation = 'Percentage change for the selected time range vs the previous month';
    } else if (headerText.includes('QUARTER')) {
      baseExplanation = 'Percentage change for the selected time range vs the previous quarter';
    } else if (headerText.includes('YEAR')) {
      baseExplanation = 'Percentage change for the selected time range vs the previous year';
    } else {
      baseExplanation =
        'Percentage change for the selected time range vs the previous time range of the same value';
    }

    // Add special cases for specific time ranges
    let specialCases = '';
    if (headerText.includes('LAST 7 DAYS')) {
      specialCases = ' (vs the 7 days before that)';
    } else if (headerText.includes('LAST 30 DAYS')) {
      specialCases = ' (vs the 30 days before that)';
    } else if (headerText.includes('LAST 90 DAYS')) {
      specialCases = ' (vs the 90 days before that)';
    }

    return baseExplanation + specialCases;
  }

  private renderTrendDisplay() {
    // Use new structured trend components if available, fallback to legacy string
    const trendComponents = this.analytics.responseRateTrendComponents;

    if (!trendComponents) {
      // Fallback to legacy string format
      return (
        <e-text variant="display" style={{ fontSize: '1em' }}>
          {this.analytics.responseRateTrend || 'No data'}
        </e-text>
      );
    }

    // Determine color based on direction
    const getColor = (direction: string) => {
      switch (direction) {
        case 'positive':
          return 'var(--color__green--600)'; // Green for positive trends
        case 'negative':
          return 'var(--color__red--600)'; // Red for negative trends
        case 'neutral':
          return 'inherit'; // Default font color for neutral
        default:
          return 'var(--color__grey--600)'; // Grey for other cases
      }
    };

    // Determine sign based on direction
    const getSign = (direction: string, magnitude: number) => {
      if (magnitude === 0) return '';
      switch (direction) {
        case 'positive':
          return '+';
        case 'negative':
          return '-';
        default:
          return '';
      }
    };

    const color = getColor(trendComponents.direction);
    const sign = getSign(trendComponents.direction, trendComponents.magnitude);

    return (
      <div>
        {trendComponents.direction === 'neutral' ? (
          // Special handling for neutral direction - show average response per day
          <e-text
            variant="display"
            style={{
              fontSize: '1em',
              fontWeight: 'bold',
            }}
          >
            {this.analytics.avgResponsesPerDay || 0}
          </e-text>
        ) : trendComponents.magnitude > 0 ? (
          // Show only the percentage value without comparison text
          <e-text
            variant="display"
            style={{
              fontSize: '1em',
              color: color,
              fontWeight: 'bold',
            }}
          >
            {sign}
            {trendComponents.magnitude}%
          </e-text>
        ) : (
          <e-text
            variant="display"
            style={{
              fontSize: '1em',
              color: color,
              fontWeight: 'bold',
            }}
          >
            {trendComponents.comparisonText}
          </e-text>
        )}
      </div>
    );
  }

  private renderSkeletonGraph() {
    return (
      <c-card style={{ padding: '0' }}>
        <div
          style={{
            width: '100%',
            height: '320px',
            backgroundColor: 'var(--color__grey--100)',
            borderRadius: '4px',
            animation: 'pulse 1.5s ease-in-out infinite',
          }}
        ></div>
      </c-card>
    );
  }

  render() {
    return (
      <div style={{ width: '100%' }}>
        {/* Header with title and filters */}
        <l-row justifyContent="space-between" align="center">
          <e-text variant="heading">Overview</e-text>

          {/* Custom Range Date Inputs - shown inline when custom range is selected */}
          {this.selectedTimeFilter === 'custom-range' && (
            <div
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: '0.75em',
                flex: '1',
                justifyContent: 'center',
              }}
            >
              <input
                type="date"
                value={this.customStartDate}
                min={this.firstResponseDate}
                max={new Date().toISOString().split('T')[0]}
                onInput={e => {
                  this.customStartDate = (e.target as HTMLInputElement).value;
                  this.validationError = '';
                  // Auto-apply when both dates are set
                  if (this.customStartDate && this.customEndDate) {
                    this.applyInlineCustomDateRange();
                  }
                }}
                style={{
                  padding: '0.5em',
                  border: 'var(--border__input)',
                  borderRadius: 'var(--border-radius)',
                  fontSize: '0.875em',
                  width: '140px',
                }}
              />

              <e-text style={{ color: 'var(--color__grey--600)', fontSize: '0.875em' }}>to</e-text>

              <input
                type="date"
                value={this.customEndDate}
                min={this.customStartDate || this.firstResponseDate}
                max={new Date().toISOString().split('T')[0]}
                onInput={e => {
                  this.customEndDate = (e.target as HTMLInputElement).value;
                  this.validationError = '';
                  // Auto-apply when both dates are set
                  if (this.customStartDate && this.customEndDate) {
                    this.applyInlineCustomDateRange();
                  }
                }}
                style={{
                  padding: '0.5em',
                  border: 'var(--border__input)',
                  borderRadius: 'var(--border-radius)',
                  fontSize: '0.875em',
                  width: '140px',
                }}
              />
            </div>
          )}

          <div style={{ position: 'relative' }}>
            <e-select
              value={this.selectedTimeFilter}
              options={JSON.stringify(this.timeFilterOptions)}
              name="timeFilter"
              variant="narrow"
              onSelectChangeEvent={this.handleTimeFilterChange}
            />
          </div>
        </l-row>

        {/* Validation Error for inline custom range */}
        {this.selectedTimeFilter === 'custom-range' && this.validationError && (
          <div style={{ marginTop: '0.5em', textAlign: 'center' }}>
            <e-text
              style={{
                color: 'var(--color__red--600)',
                fontSize: '0.875em',
                display: 'inline-block',
                padding: '0.5em 1em',
                backgroundColor: 'var(--color__red--50)',
                border: '1px solid var(--color__red--200)',
                borderRadius: 'var(--border-radius)',
              }}
            >
              {this.validationError}
            </e-text>
          </div>
        )}

        <l-spacer value={2}></l-spacer>

        {/* Error State */}
        {this.error && (
          <c-card>
            <div style={{ textAlign: 'center', padding: '2em' }}>
              <e-text>
                <strong>{this.error}</strong>
              </e-text>
            </div>
          </c-card>
        )}

        {/* Overview Cards - Show skeleton while loading, real data when loaded */}
        {!this.error &&
          (this.loading ? (
            <l-row>
              <c-card style={{ flex: '1', marginRight: '1em' }}>
                <div style={{ textAlign: 'center' }}>
                  <div
                    style={{
                      height: '3em',
                      backgroundColor: 'var(--color__grey--100)',
                      borderRadius: '4px',
                      animation: 'pulse 1.5s ease-in-out infinite',
                    }}
                  ></div>
                </div>
              </c-card>
              <c-card style={{ flex: '1', marginRight: '1em' }}>
                <div style={{ textAlign: 'center' }}>
                  <div
                    style={{
                      height: '3em',
                      backgroundColor: 'var(--color__grey--100)',
                      borderRadius: '4px',
                      animation: 'pulse 1.5s ease-in-out infinite',
                    }}
                  ></div>
                </div>
              </c-card>
              <c-card style={{ flex: '1' }}>
                <div style={{ textAlign: 'center' }}>
                  <div
                    style={{
                      height: '3em',
                      backgroundColor: 'var(--color__grey--100)',
                      borderRadius: '4px',
                      animation: 'pulse 1.5s ease-in-out infinite',
                    }}
                  ></div>
                </div>
              </c-card>
            </l-row>
          ) : (
            <l-row>
              <c-card style={{ flex: '1', marginRight: '1em' }}>
                <div style={{ textAlign: 'center' }}>
                  <e-text variant="footnote">TOTAL RESPONSES</e-text>
                  <l-spacer value={0.25}></l-spacer>
                  <e-text variant="display">{this.totalCount || 0}</e-text>
                </div>
              </c-card>
              <c-card style={{ flex: '1', marginRight: '1em' }}>
                <div style={{ textAlign: 'center' }}>
                  <div
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      gap: '0.5em',
                    }}
                  >
                    <e-text variant="footnote">
                      {this.analytics.headerText || 'AVG. RESPONSES PER DAY'}
                    </e-text>
                    {this.analytics.headerText &&
                      this.analytics.headerText !== 'AVG. RESPONSES PER DAY' && (
                        <e-image
                          src="../../../assets/icon/light/info-light.svg"
                          width="1em"
                          title={this.getTrendTooltipText()}
                          style={{ cursor: 'help' }}
                        ></e-image>
                      )}
                  </div>
                  <l-spacer value={0.25}></l-spacer>
                  {this.renderTrendDisplay()}
                </div>
              </c-card>
              <c-card style={{ flex: '1' }}>
                <div style={{ textAlign: 'center' }}>
                  <e-text variant="footnote">AVG. COMPLETION TIME</e-text>
                  <l-spacer value={0.25}></l-spacer>
                  <e-text variant="display">{this.analytics.avgCompletionTime || 0}s</e-text>
                </div>
              </c-card>
            </l-row>
          ))}

        {!this.error && (
          <div>
            <l-spacer value={2}></l-spacer>

            {/* Graph - Show skeleton while loading, real graph when loaded */}
            {this.loading ? (
              this.renderSkeletonGraph()
            ) : (
              <c-card style={{ padding: '0' }}>
                <e-text variant="footnote">RESPONSES OVER TIME</e-text>
                <l-spacer value={0.5}></l-spacer>
                <div
                  id="responses-graph"
                  style={{
                    width: '100%',
                    height: '320px',
                    position: 'relative',
                    overflow: 'hidden',
                    zIndex: '1',
                    display:
                      this.analytics.responsesByDay &&
                      Array.isArray(this.analytics.responsesByDay) &&
                      this.analytics.responsesByDay.length > 0
                        ? 'block'
                        : 'none',
                  }}
                ></div>
                {(!this.analytics.responsesByDay ||
                  !Array.isArray(this.analytics.responsesByDay) ||
                  this.analytics.responsesByDay.length === 0) && (
                  <div style={{ padding: '2em', textAlign: 'center' }}>
                    <e-text>No response data available for chart</e-text>
                  </div>
                )}
              </c-card>
            )}
          </div>
        )}
      </div>
    );
  }
}
